/**
 * Rate Limiting Handler untuk Gemini API
 * 
 * Handles rate limiting untuk free tier dan paid tier Gemini API
 * Implements request queuing, retry logic, dan usage monitoring
 */

const logger = require('./src/utils/logger');

// Rate limit configurations
const RATE_LIMITS = {
  free: {
    requestsPerMinute: 15,
    requestsPerDay: 1500,
    tokensPerMinute: 1000000,
    tier: 'free'
  },
  paid: {
    requestsPerMinute: 1000,
    requestsPerDay: null, // No daily limit
    tokensPerMinute: 4000000,
    tier: 'paid'
  }
};

class GeminiRateLimitHandler {
  constructor(tier = 'free') {
    this.tier = tier;
    this.limits = RATE_LIMITS[tier];
    
    // Request tracking
    this.requestCount = 0;
    this.dailyRequestCount = 0;
    this.tokenCount = 0;
    
    // Time tracking
    this.lastMinuteReset = Date.now();
    this.lastDayReset = Date.now();
    this.lastTokenReset = Date.now();
    
    // Queue for pending requests
    this.requestQueue = [];
    this.processing = false;
    
    // Usage statistics
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      rateLimitHits: 0,
      totalTokensUsed: 0,
      estimatedCost: 0
    };
    
    logger.info('Gemini Rate Limit Handler initialized', {
      tier: this.tier,
      limits: this.limits
    });
  }

  /**
   * Reset counters if time windows have passed
   */
  resetCountersIfNeeded() {
    const now = Date.now();
    
    // Reset minute counter
    if (now - this.lastMinuteReset >= 60000) {
      this.requestCount = 0;
      this.tokenCount = 0;
      this.lastMinuteReset = now;
    }
    
    // Reset daily counter (only for free tier)
    if (this.limits.requestsPerDay && now - this.lastDayReset >= 86400000) {
      this.dailyRequestCount = 0;
      this.lastDayReset = now;
    }
  }

  /**
   * Check if request can be made within rate limits
   */
  canMakeRequest(estimatedTokens = 1000) {
    this.resetCountersIfNeeded();
    
    // Check requests per minute
    if (this.requestCount >= this.limits.requestsPerMinute) {
      return {
        allowed: false,
        reason: 'requests_per_minute_exceeded',
        waitTime: 60000 - (Date.now() - this.lastMinuteReset)
      };
    }
    
    // Check daily requests (free tier only)
    if (this.limits.requestsPerDay && this.dailyRequestCount >= this.limits.requestsPerDay) {
      return {
        allowed: false,
        reason: 'daily_requests_exceeded',
        waitTime: 86400000 - (Date.now() - this.lastDayReset)
      };
    }
    
    // Check tokens per minute
    if (this.tokenCount + estimatedTokens > this.limits.tokensPerMinute) {
      return {
        allowed: false,
        reason: 'tokens_per_minute_exceeded',
        waitTime: 60000 - (Date.now() - this.lastMinuteReset)
      };
    }
    
    return { allowed: true };
  }

  /**
   * Update counters after successful request
   */
  updateCounters(tokensUsed) {
    this.requestCount++;
    this.dailyRequestCount++;
    this.tokenCount += tokensUsed;
    
    // Update statistics
    this.stats.totalRequests++;
    this.stats.successfulRequests++;
    this.stats.totalTokensUsed += tokensUsed;
    
    // Calculate estimated cost (for paid tier)
    if (this.tier === 'paid') {
      const inputCost = (tokensUsed * 0.5) * 0.075 / 1000; // Rough estimate
      const outputCost = (tokensUsed * 0.5) * 0.30 / 1000;
      this.stats.estimatedCost += inputCost + outputCost;
    }
  }

  /**
   * Handle rate limit exceeded
   */
  handleRateLimitExceeded(reason, waitTime) {
    this.stats.rateLimitHits++;
    
    logger.warn('Rate limit exceeded', {
      tier: this.tier,
      reason,
      waitTime,
      currentCounts: {
        requestsThisMinute: this.requestCount,
        dailyRequests: this.dailyRequestCount,
        tokensThisMinute: this.tokenCount
      }
    });
  }

  /**
   * Wait for rate limit window to reset
   */
  async waitForRateLimit(waitTime) {
    logger.info(`Waiting ${waitTime}ms for rate limit reset`);
    await new Promise(resolve => setTimeout(resolve, waitTime));
  }

  /**
   * Process request with rate limiting
   */
  async processRequest(requestFn, estimatedTokens = 1000, maxRetries = 3) {
    let retries = 0;
    
    while (retries < maxRetries) {
      const canProceed = this.canMakeRequest(estimatedTokens);
      
      if (!canProceed.allowed) {
        this.handleRateLimitExceeded(canProceed.reason, canProceed.waitTime);
        
        // For free tier, wait for rate limit reset
        if (this.tier === 'free') {
          await this.waitForRateLimit(canProceed.waitTime);
          retries++;
          continue;
        } else {
          // For paid tier, this shouldn't happen often
          throw new Error(`Rate limit exceeded: ${canProceed.reason}`);
        }
      }
      
      try {
        // Make the actual request
        const result = await requestFn();
        
        // Estimate tokens used (rough calculation)
        const tokensUsed = this.estimateTokensUsed(result);
        this.updateCounters(tokensUsed);
        
        logger.debug('Request processed successfully', {
          tier: this.tier,
          tokensUsed,
          stats: this.getUsageStats()
        });
        
        return result;
        
      } catch (error) {
        this.stats.failedRequests++;
        
        // Check if it's a rate limit error from API
        if (error.status === 429 || error.message?.includes('rate limit')) {
          logger.warn('API rate limit error received', { error: error.message });
          retries++;
          
          // Exponential backoff
          const backoffTime = Math.min(1000 * Math.pow(2, retries), 30000);
          await new Promise(resolve => setTimeout(resolve, backoffTime));
          continue;
        }
        
        // Other errors, don't retry
        throw error;
      }
    }
    
    throw new Error(`Max retries (${maxRetries}) exceeded for rate limited request`);
  }

  /**
   * Estimate tokens used in request/response
   */
  estimateTokensUsed(result) {
    // Rough estimation: 1 token ≈ 4 characters
    if (result && result.text) {
      return Math.ceil(result.text.length / 4);
    }
    return 1000; // Default estimate
  }

  /**
   * Get current usage statistics
   */
  getUsageStats() {
    return {
      ...this.stats,
      currentCounts: {
        requestsThisMinute: this.requestCount,
        dailyRequests: this.dailyRequestCount,
        tokensThisMinute: this.tokenCount
      },
      limits: this.limits,
      tier: this.tier
    };
  }

  /**
   * Log usage statistics
   */
  logUsageStats() {
    const stats = this.getUsageStats();
    
    logger.info('Gemini API usage statistics', stats);
    
    // Warning if approaching limits
    if (this.tier === 'free') {
      const minuteUsage = (this.requestCount / this.limits.requestsPerMinute) * 100;
      const dailyUsage = (this.dailyRequestCount / this.limits.requestsPerDay) * 100;
      
      if (minuteUsage > 80) {
        logger.warn(`Approaching minute rate limit: ${minuteUsage.toFixed(1)}%`);
      }
      
      if (dailyUsage > 80) {
        logger.warn(`Approaching daily rate limit: ${dailyUsage.toFixed(1)}%`);
      }
    }
  }

  /**
   * Check if we should upgrade to paid tier
   */
  shouldUpgradeTier() {
    if (this.tier === 'paid') return false;
    
    const hourlyProjection = this.stats.totalRequests * 24;
    const dailyProjection = hourlyProjection;
    
    return dailyProjection > this.limits.requestsPerDay * 0.8;
  }
}

// Export singleton instance
const tier = process.env.GEMINI_TIER || 'free';
const rateLimitHandler = new GeminiRateLimitHandler(tier);

// Log usage stats every 5 minutes
setInterval(() => {
  rateLimitHandler.logUsageStats();
}, 5 * 60 * 1000);

module.exports = rateLimitHandler;
