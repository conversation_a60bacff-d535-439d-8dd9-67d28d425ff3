# Konfigurasi Optimasi untuk 100 Concurrent Users

## 1. Database Connection Pool Optimization

### Current Configuration (Tidak Optimal untuk 100 Users)
```javascript
pool: {
  max: 5,        // Terlalu kecil untuk 100 concurrent users
  min: 0,
  acquire: 30000,
  idle: 10000
}
```

### Recommended Configuration untuk 100 Concurrent Users
```javascript
pool: {
  max: 25,       // Increased untuk handle concurrent load
  min: 5,        // Maintain minimum connections
  acquire: 60000, // Increased timeout untuk high load
  idle: 30000,   // Longer idle time untuk stability
  evict: 10000,  // Connection eviction interval
  handleDisconnects: true
}
```

### Environment Variables untuk Database Optimization
```bash
# Database Pool Configuration
DB_POOL_MAX=25
DB_POOL_MIN=5
DB_POOL_ACQUIRE=60000
DB_POOL_IDLE=30000
DB_POOL_EVICT=10000

# PostgreSQL Configuration
DB_CONNECTION_TIMEOUT=30000
DB_STATEMENT_TIMEOUT=30000
DB_QUERY_TIMEOUT=30000
```

### PostgreSQL Server Configuration (postgresql.conf)
```ini
# Connection Settings
max_connections = 200                    # Increased from default 100
shared_buffers = 256MB                   # 25% of RAM for small systems
effective_cache_size = 1GB               # 75% of RAM for small systems

# Performance Settings
work_mem = 4MB                           # Per-operation memory
maintenance_work_mem = 64MB              # Maintenance operations
checkpoint_completion_target = 0.9       # Checkpoint spread
wal_buffers = 16MB                       # WAL buffer size

# Logging for monitoring
log_min_duration_statement = 1000        # Log slow queries (>1s)
log_connections = on                     # Log connections
log_disconnections = on                  # Log disconnections
log_lock_waits = on                      # Log lock waits
```

## 2. RabbitMQ Optimization

### Current Configuration
```javascript
// analysis-worker/.env
WORKER_CONCURRENCY=3
```

### Recommended Configuration untuk 100 Concurrent Users
```bash
# RabbitMQ Connection
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_HEARTBEAT=60
RABBITMQ_CONNECTION_TIMEOUT=30000

# Queue Configuration
QUEUE_DURABLE=true
MESSAGE_PERSISTENT=true
PREFETCH_COUNT=5                         # Increased from 3

# Worker Configuration
WORKER_CONCURRENCY=5                     # Increased from 3
MAX_RETRIES=3
RETRY_DELAY=5000
PROCESSING_TIMEOUT=300000                # 5 minutes
HEARTBEAT_INTERVAL=30000
```

### RabbitMQ Server Configuration (rabbitmq.conf)
```ini
# Connection limits
tcp_listeners.default = 5672
num_acceptors.tcp = 10
handshake_timeout = 10000
heartbeat = 60

# Memory and disk limits
vm_memory_high_watermark.relative = 0.6
disk_free_limit.relative = 2.0

# Queue settings
queue_master_locator = min-masters
```

## 3. Analysis Worker Scaling

### Recommended Worker Instances untuk 100 Concurrent Users

**Calculation:**
- 100 concurrent submissions
- Average processing time: 30-60 seconds per assessment
- Worker concurrency: 5 jobs per instance
- Target: Process all jobs within 2-3 minutes

**Recommended Setup:**
```bash
# Minimum 4 worker instances
# Total capacity: 4 instances × 5 concurrency = 20 concurrent jobs
# Processing time: ~5 minutes for 100 jobs

# Optimal 6-8 worker instances  
# Total capacity: 6-8 instances × 5 concurrency = 30-40 concurrent jobs
# Processing time: ~3-4 minutes for 100 jobs
```

### Worker Instance Configuration
```bash
# Instance 1
WORKER_INSTANCE_ID=worker-1
WORKER_CONCURRENCY=5
LOG_FILE=logs/analysis-worker-1.log

# Instance 2  
WORKER_INSTANCE_ID=worker-2
WORKER_CONCURRENCY=5
LOG_FILE=logs/analysis-worker-2.log

# Instance 3
WORKER_INSTANCE_ID=worker-3
WORKER_CONCURRENCY=5
LOG_FILE=logs/analysis-worker-3.log

# Instance 4
WORKER_INSTANCE_ID=worker-4
WORKER_CONCURRENCY=5
LOG_FILE=logs/analysis-worker-4.log
```

## 4. API Gateway & Assessment Service Optimization

### Rate Limiting Configuration
```javascript
// Current: 10 requests per hour per user (terlalu ketat)
// Recommended untuk load testing:
rateLimit: {
  windowMs: 15 * 60 * 1000,              // 15 minutes
  max: 50,                               // 50 requests per 15 minutes
  message: 'Too many requests',
  standardHeaders: true,
  legacyHeaders: false,
}
```

### Express.js Configuration
```javascript
// Increase request limits
app.use(express.json({ 
  limit: '10mb',                         // Increased payload limit
  extended: true 
}));

// Connection timeout
app.use(timeout('30s'));                 // 30 second timeout

// Keep-alive connections
app.use((req, res, next) => {
  res.setHeader('Connection', 'keep-alive');
  res.setHeader('Keep-Alive', 'timeout=30, max=100');
  next();
});
```

## 5. Memory and Resource Optimization

### Node.js Memory Configuration
```bash
# Increase Node.js heap size
NODE_OPTIONS="--max-old-space-size=2048"  # 2GB heap

# Garbage collection optimization
NODE_OPTIONS="--max-old-space-size=2048 --optimize-for-size"
```

### Job Tracker Optimization
```javascript
// assessment-service/src/jobs/jobTracker.js
// Add memory management for high load

const MAX_JOBS_IN_MEMORY = 1000;          // Limit in-memory jobs
const CLEANUP_INTERVAL = 5 * 60 * 1000;   // 5 minutes cleanup

// Implement LRU cache for job tracking
const LRU = require('lru-cache');
const jobs = new LRU({
  max: MAX_JOBS_IN_MEMORY,
  ttl: 1000 * 60 * 60 * 2                 // 2 hours TTL
});
```

## 6. Monitoring and Alerting

### Health Check Endpoints
```javascript
// Add detailed health checks
app.get('/health/detailed', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    database: {
      connected: true,
      activeConnections: pool.totalCount,
      idleConnections: pool.idleCount
    },
    queue: {
      connected: true,
      messageCount: queueStats.messageCount
    },
    memory: {
      used: process.memoryUsage().heapUsed,
      total: process.memoryUsage().heapTotal
    }
  });
});
```

### Logging Configuration
```bash
# Structured logging untuk monitoring
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE=logs/service.log
LOG_MAX_SIZE=100MB
LOG_MAX_FILES=5

# Performance logging
ENABLE_PERFORMANCE_LOGGING=true
SLOW_QUERY_THRESHOLD=1000               # Log queries > 1s
```

## 7. Network Configuration untuk 1 Jaringan

### Load Balancer Configuration (Nginx)
```nginx
upstream atma_api_gateway {
    least_conn;                          # Load balancing method
    server 127.0.0.1:3000 max_fails=3 fail_timeout=30s;
    keepalive 32;                        # Keep-alive connections
}

server {
    listen 80;
    server_name atma-api.local;
    
    # Rate limiting per IP
    limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;
    limit_req zone=api burst=20 nodelay;
    
    # Connection limits
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
    limit_conn conn_limit_per_ip 20;
    
    location / {
        proxy_pass http://atma_api_gateway;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
}
```

### Network Optimization
```bash
# TCP optimization untuk high concurrent connections
echo 'net.core.somaxconn = 1024' >> /etc/sysctl.conf
echo 'net.core.netdev_max_backlog = 5000' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 1024' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_keepalive_time = 600' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_keepalive_intvl = 60' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_keepalive_probes = 3' >> /etc/sysctl.conf

# Apply changes
sysctl -p
```
