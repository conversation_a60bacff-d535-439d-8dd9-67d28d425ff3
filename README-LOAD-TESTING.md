# 🚀 ATMA Backend - Load Testing untuk 100 Concurrent Users

## 📋 Overview

Simulasi dan konfigurasi untuk menangani 100 user yang login dan menggunakan layanan assessment/submit bersamaan dalam 1 jaringan yang sama.

## 🎯 Hasil Analisis

### Current System Capacity
- **Default Configuration**: ~10-15 concurrent users
- **Bottlenecks**: Database connections (5), Single worker (concurrency 3), Memory limits

### Optimized System Capacity  
- **Target**: 100 concurrent users
- **Processing Time**: 3-4 menit untuk 100 assessments
- **Success Rate**: 95-100%

## ⚙️ Konfigurasi yang Diperlukan

### 1. Database Connection Pool
```javascript
// Dari: max: 5 connections
// Ke: max: 25 connections per service
pool: {
  max: 25,
  min: 5, 
  acquire: 60000,
  idle: 30000
}
```

### 2. Analysis Worker Scaling
```bash
# Dari: 1 worker instance (3 concurrency)
# Ke: 6 worker instances (5 concurrency each)
Total Capacity: 30 concurrent jobs
Recommended Workers: 6 instances
```

### 3. RabbitMQ Optimization
```bash
WORKER_CONCURRENCY=5
PREFETCH_COUNT=5
vm_memory_high_watermark.relative=0.6
max_connections=1000
```

## 🏗️ Infrastructure Requirements

### Server Specifications
```yaml
CPU: 8 cores (Intel i7/AMD Ryzen 7)
RAM: 16GB minimum, 32GB recommended
Storage: 500GB SSD
Network: 1Gbps Ethernet
```

### Network Configuration
```
Network: ***********/24
Server: *********** (ATMA Backend)
Database: *********** (PostgreSQL)
Queue: *********** (RabbitMQ)
Clients: *************-199 (100 users)
Bandwidth: 10Mbps internet, 1Gbps internal
```

## 🚀 Quick Start

### 1. Setup Optimizations
```bash
# Apply database optimizations
# Edit database config files dengan pool settings baru

# Setup RabbitMQ
copy rabbitmq-optimization.conf /etc/rabbitmq/rabbitmq.conf
bash setup-rabbitmq-policies.sh
```

### 2. Start Multiple Workers
```bash
# Windows
start-multiple-workers.bat

# Manual
cd analysis-worker
set WORKER_INSTANCE_ID=worker-1 && npm start
# Repeat untuk worker-2 sampai worker-6
```

### 3. Run Load Test
```bash
# Install dependencies
npm install axios uuid

# Run test
run-load-test.bat

# Atau manual
CONCURRENT_USERS=100 node load-test-100-users.js
```

## 📊 Expected Results

### Successful Load Test
```
✅ Total Users: 100
✅ Successful Logins: 95-100 (95-100%)
✅ Successful Submissions: 95-100 (95-100%)
✅ Average Response Time: <3000ms
✅ Queue Processing: 3-4 minutes
✅ Max Queue Position: ~100
✅ Throughput: 25-30 requests/second
```

### Performance Metrics
```
Login Response Time: 500-2000ms
Submit Response Time: 1000-3000ms
Queue Processing Rate: 25-30 jobs/minute
Total Test Duration: 5-8 minutes
System Resource Usage: <80%
```

## 🔧 Files Created

### Load Testing
- `load-test-100-users.js` - Main load testing script
- `run-load-test.bat` - Windows batch script untuk run test

### Configuration
- `config-optimizations.md` - Database dan system optimizations
- `rabbitmq-optimization.conf` - RabbitMQ configuration
- `setup-rabbitmq-policies.sh` - RabbitMQ policies setup

### Worker Scaling
- `start-multiple-workers.bat` - Script untuk start 6 workers
- `worker-scaling-guide.md` - Detailed worker scaling guide

### Infrastructure
- `network-infrastructure-config.md` - Network dan infrastructure setup
- `LOAD-TESTING-GUIDE.md` - Comprehensive testing guide

## 📈 Monitoring

### During Load Test
```bash
# System monitoring
htop                    # CPU, Memory usage
iftop -i eth0          # Network bandwidth
iostat -x 1            # Disk I/O

# Application monitoring
rabbitmqctl list_queues name messages consumers
tail -f analysis-worker/logs/analysis-worker-*.log
curl http://localhost:3000/health
```

### Key Metrics to Watch
```
CPU Usage: <80%
Memory Usage: <80%
Network Bandwidth: <80%
Queue Length: <100 messages
Active Database Connections: <20 per service
Response Times: <3000ms
Error Rate: <5%
```

## 🚨 Troubleshooting

### Common Issues
1. **High Login Failures**: Increase database connections
2. **Submit Timeouts**: Scale workers, check queue
3. **Queue Backup**: Add more workers
4. **Memory Issues**: Increase RAM, optimize job tracker
5. **Network Congestion**: Check bandwidth, upgrade infrastructure

### Quick Fixes
```bash
# Restart services
systemctl restart postgresql
systemctl restart rabbitmq-server
pm2 restart all

# Clear queue if needed
node clear-queue.js

# Check service health
curl http://localhost:3000/health
rabbitmqctl status
```

## 📋 Implementation Timeline

### Day 1: Infrastructure Setup
- [ ] Server hardware preparation
- [ ] Network configuration
- [ ] Database optimization
- [ ] RabbitMQ setup

### Day 2: Application Optimization
- [ ] Worker scaling implementation
- [ ] Load balancer setup
- [ ] Monitoring configuration
- [ ] Initial testing

### Day 3: Load Testing & Tuning
- [ ] Full load testing
- [ ] Performance tuning
- [ ] Issue resolution
- [ ] Documentation

## 🎯 Success Criteria

### Performance Targets
- ✅ 100 concurrent users supported
- ✅ 95%+ success rate untuk login dan submit
- ✅ <3000ms average response time
- ✅ <5 minutes total processing time
- ✅ System stability (no crashes)

### Infrastructure Targets
- ✅ <80% resource utilization
- ✅ Network latency <100ms
- ✅ Database response <1000ms
- ✅ Queue processing <4 minutes
- ✅ Zero data loss

## 📞 Support

Untuk pertanyaan atau issues:
1. Check troubleshooting guide
2. Review log files
3. Monitor system metrics
4. Consult configuration documentation

---

**Ready to handle 100 concurrent users! 🚀**
