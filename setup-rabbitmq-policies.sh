#!/bin/bash
# RabbitMQ Policies Setup untuk High Load
# Script untuk mengatur policies RabbitM<PERSON> yang optimal untuk 100 concurrent users

echo "Setting up RabbitMQ policies for high load..."

# Enable management plugin
rabbitmq-plugins enable rabbitmq_management

# Set queue policies untuk high availability dan performance
rabbitmqctl set_policy ha-assessment "^assessment_.*" \
  '{"ha-mode":"exactly","ha-params":2,"ha-sync-mode":"automatic","max-length":10000,"overflow":"reject-publish"}' \
  --priority 1 \
  --apply-to queues

# Set exchange policies
rabbitmqctl set_policy ha-exchange "^atma_.*" \
  '{"ha-mode":"all"}' \
  --priority 1 \
  --apply-to exchanges

# Set TTL policy untuk dead letter queue
rabbitmqctl set_policy dlq-ttl ".*_dlq$" \
  '{"message-ttl":3600000,"max-length":1000}' \
  --priority 2 \
  --apply-to queues

# Set memory-based flow control
rabbitmqctl set_policy memory-limit ".*" \
  '{"max-length-bytes":104857600}' \
  --priority 0 \
  --apply-to queues

# Create virtual host untuk production (optional)
# rabbitmqctl add_vhost atma_production
# rabbitmqctl set_permissions -p atma_production guest ".*" ".*" ".*"

# Set user limits
rabbitmqctl set_user_limits guest '{"max-connections": 100, "max-channels": 200}'

# Show current policies
echo "Current RabbitMQ policies:"
rabbitmqctl list_policies

echo "RabbitMQ optimization setup completed!"
echo ""
echo "Recommended monitoring commands:"
echo "  rabbitmqctl list_queues name messages consumers"
echo "  rabbitmqctl list_connections"
echo "  rabbitmqctl list_channels"
echo "  rabbitmq-diagnostics memory_breakdown"
