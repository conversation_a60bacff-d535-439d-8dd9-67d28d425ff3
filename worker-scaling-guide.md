# Analysis Worker Scaling Guide untuk 100 Concurrent Users

## Perhitungan <PERSON>itas Worker

### Skenario Load Testing
- **100 concurrent users** submit assessment bersamaan
- **Average processing time**: 30-60 detik per assessment (tergantung AI response time)
- **Target completion time**: Semua assessment selesai dalam 3-5 menit

### Konfigurasi Worker yang Direkomendasikan

#### Option 1: Conservative (Minimum Viable)
```bash
Worker Instances: 4
Concurrency per Worker: 5
Total Concurrent Jobs: 20
Estimated Processing Time: 5-6 menit untuk 100 jobs
```

#### Option 2: Optimal (Recommended)
```bash
Worker Instances: 6  
Concurrency per Worker: 5
Total Concurrent Jobs: 30
Estimated Processing Time: 3-4 menit untuk 100 jobs
```

#### Option 3: High Performance (Maximum)
```bash
Worker Instances: 8
Concurrency per Worker: 5  
Total Concurrent Jobs: 40
Estimated Processing Time: 2-3 menit untuk 100 jobs
```

## Environment Variables untuk Scaling

### Worker Instance Configuration
```bash
# Worker 1
WORKER_INSTANCE_ID=worker-1
WORKER_CONCURRENCY=5
LOG_FILE=logs/analysis-worker-1.log
HEARTBEAT_INTERVAL=30000

# Worker 2  
WORKER_INSTANCE_ID=worker-2
WORKER_CONCURRENCY=5
LOG_FILE=logs/analysis-worker-2.log
HEARTBEAT_INTERVAL=30000

# Worker 3
WORKER_INSTANCE_ID=worker-3
WORKER_CONCURRENCY=5
LOG_FILE=logs/analysis-worker-3.log
HEARTBEAT_INTERVAL=30000

# Worker 4
WORKER_INSTANCE_ID=worker-4
WORKER_CONCURRENCY=5
LOG_FILE=logs/analysis-worker-4.log
HEARTBEAT_INTERVAL=30000

# Worker 5
WORKER_INSTANCE_ID=worker-5
WORKER_CONCURRENCY=5
LOG_FILE=logs/analysis-worker-5.log
HEARTBEAT_INTERVAL=30000

# Worker 6
WORKER_INSTANCE_ID=worker-6
WORKER_CONCURRENCY=5
LOG_FILE=logs/analysis-worker-6.log
HEARTBEAT_INTERVAL=30000
```

### Shared Configuration untuk Semua Workers
```bash
# RabbitMQ Configuration
RABBITMQ_URL=amqp://localhost:5672
QUEUE_NAME=assessment_analysis
EXCHANGE_NAME=atma_exchange
ROUTING_KEY=analysis.process
PREFETCH_COUNT=5

# AI Configuration
GOOGLE_AI_API_KEY=your_api_key_here
GOOGLE_AI_MODEL=gemini-2.5-flash
AI_TEMPERATURE=0.7
PROCESSING_TIMEOUT=300000

# Performance Configuration
MAX_RETRIES=3
RETRY_DELAY=5000
HEARTBEAT_INTERVAL=30000

# Memory Configuration
NODE_OPTIONS="--max-old-space-size=1024"
```

## Resource Requirements

### Memory Requirements per Worker
```
Base Memory: ~100MB per worker
Processing Memory: ~50MB per concurrent job
Total per Worker: 100MB + (5 jobs × 50MB) = 350MB
Total for 6 Workers: 6 × 350MB = ~2.1GB
```

### CPU Requirements
```
Minimum: 4 CPU cores
Recommended: 6-8 CPU cores
Optimal: 1 core per worker instance + 2 cores for system
```

### Network Requirements
```
Bandwidth: ~1-2 Mbps per worker untuk AI API calls
Concurrent Connections: ~30-50 connections ke Google AI API
```

## Monitoring dan Health Checks

### Worker Health Monitoring
```javascript
// Tambahkan ke worker.js
const healthCheck = {
  workerId: process.env.WORKER_INSTANCE_ID,
  startTime: new Date(),
  processedJobs: 0,
  failedJobs: 0,
  currentLoad: 0,
  memoryUsage: process.memoryUsage(),
  lastHeartbeat: new Date()
};

// Update health check setiap 30 detik
setInterval(() => {
  healthCheck.lastHeartbeat = new Date();
  healthCheck.memoryUsage = process.memoryUsage();
  healthCheck.currentLoad = getCurrentJobCount();
  
  logger.info('Worker health check', healthCheck);
}, 30000);
```

### Queue Monitoring Commands
```bash
# Check queue status
rabbitmqctl list_queues name messages consumers

# Check worker connections
rabbitmqctl list_connections

# Monitor memory usage
rabbitmq-diagnostics memory_breakdown

# Check queue message rates
rabbitmqctl list_queues name message_stats.publish_details.rate message_stats.deliver_get_details.rate
```

## Load Testing Scenarios

### Scenario 1: Gradual Load
```bash
# Start dengan 10 users, tambah 10 setiap 30 detik
CONCURRENT_USERS=10 node load-test-100-users.js
# Wait 30 seconds
CONCURRENT_USERS=20 node load-test-100-users.js
# Continue until 100 users
```

### Scenario 2: Burst Load
```bash
# Langsung 100 users bersamaan
CONCURRENT_USERS=100 node load-test-100-users.js
```

### Scenario 3: Sustained Load
```bash
# 100 users setiap 5 menit selama 30 menit
for i in {1..6}; do
  CONCURRENT_USERS=100 node load-test-100-users.js
  sleep 300
done
```

## Troubleshooting

### Common Issues dan Solutions

#### 1. Queue Backup
**Symptoms**: Queue message count terus naik
**Solution**: 
- Tambah worker instances
- Increase worker concurrency
- Check AI API rate limits

#### 2. Memory Issues
**Symptoms**: Worker crashes dengan OOM errors
**Solution**:
- Increase NODE_OPTIONS max-old-space-size
- Reduce worker concurrency
- Add memory monitoring

#### 3. Connection Issues
**Symptoms**: RabbitMQ connection errors
**Solution**:
- Check RabbitMQ connection limits
- Increase connection pool size
- Add connection retry logic

#### 4. AI API Rate Limits
**Symptoms**: 429 errors dari Google AI
**Solution**:
- Add exponential backoff
- Distribute load across multiple API keys
- Implement request queuing

## Performance Tuning Tips

### 1. Worker Concurrency Tuning
```bash
# Start conservative
WORKER_CONCURRENCY=3

# Monitor performance dan gradually increase
WORKER_CONCURRENCY=5

# Maximum recommended (depends on resources)
WORKER_CONCURRENCY=8
```

### 2. RabbitMQ Prefetch Tuning
```bash
# Conservative (default)
PREFETCH_COUNT=3

# Optimal untuk high load
PREFETCH_COUNT=5

# High performance (requires more memory)
PREFETCH_COUNT=10
```

### 3. AI Processing Optimization
```bash
# Faster model untuk load testing
GOOGLE_AI_MODEL=gemini-1.5-flash

# Reduce temperature untuk faster processing
AI_TEMPERATURE=0.3

# Shorter timeout untuk faster failure detection
PROCESSING_TIMEOUT=180000  # 3 minutes
```
