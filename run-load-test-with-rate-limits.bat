@echo off
REM Load Test Runner dengan Rate Limiting Awareness
REM Script untuk menjalankan load test dengan konfigurasi yang tepat untuk Gemini API tiers

echo ========================================
echo ATMA Backend Load Test - Rate Limit Aware
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if required packages are installed
if not exist "node_modules\axios" (
    echo Installing required packages...
    npm install axios uuid
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install packages
        pause
        exit /b 1
    )
)

echo Select Gemini API Tier:
echo 1. Free Tier (15 requests/minute, 1,500/day)
echo 2. Paid Tier (1,000 requests/minute, unlimited)
echo.
set /p tier_choice="Enter choice (1 or 2): "

if "%tier_choice%"=="1" (
    set GEMINI_TIER=free
    set RECOMMENDED_USERS=10
    set RECOMMENDED_DELAY=4000
    echo.
    echo ⚠️  WARNING: Free tier selected
    echo Recommended configuration:
    echo - Max users: 10
    echo - Delay: 4000ms between submissions
    echo.
) else if "%tier_choice%"=="2" (
    set GEMINI_TIER=paid
    set RECOMMENDED_USERS=100
    set RECOMMENDED_DELAY=0
    echo.
    echo ✅ Paid tier selected
    echo Recommended configuration:
    echo - Max users: 100
    echo - Delay: 0ms (no delay needed)
    echo.
) else (
    echo Invalid choice. Defaulting to free tier.
    set GEMINI_TIER=free
    set RECOMMENDED_USERS=10
    set RECOMMENDED_DELAY=4000
)

echo.
echo Select test configuration:
echo 1. Recommended settings (%RECOMMENDED_USERS% users, %RECOMMENDED_DELAY%ms delay)
echo 2. Custom settings
echo 3. Stress test (100 users regardless of tier - may fail on free tier)
echo.
set /p config_choice="Enter choice (1, 2, or 3): "

if "%config_choice%"=="1" (
    set CONCURRENT_USERS=%RECOMMENDED_USERS%
    set ASSESSMENT_DELAY=%RECOMMENDED_DELAY%
) else if "%config_choice%"=="2" (
    set /p CONCURRENT_USERS="Enter number of concurrent users: "
    set /p ASSESSMENT_DELAY="Enter delay between submissions (ms): "
) else if "%config_choice%"=="3" (
    set CONCURRENT_USERS=100
    set ASSESSMENT_DELAY=0
    echo.
    echo ⚠️  WARNING: Stress test mode
    if "%GEMINI_TIER%"=="free" (
        echo This WILL fail on free tier due to rate limits!
        echo Expected: High failure rate, 429 errors
        echo.
        set /p continue="Continue anyway? (y/N): "
        if /i not "%continue%"=="y" (
            echo Test cancelled
            pause
            exit /b 1
        )
    )
) else (
    echo Invalid choice. Using recommended settings.
    set CONCURRENT_USERS=%RECOMMENDED_USERS%
    set ASSESSMENT_DELAY=%RECOMMENDED_DELAY%
)

REM Set other environment variables
set API_GATEWAY_URL=http://localhost:3000

echo.
echo ========================================
echo Test Configuration:
echo ========================================
echo API Gateway URL: %API_GATEWAY_URL%
echo Gemini API Tier: %GEMINI_TIER%
echo Concurrent Users: %CONCURRENT_USERS%
echo Assessment Delay: %ASSESSMENT_DELAY%ms
echo.

REM Check if services are running
echo Checking if services are running...
curl -s %API_GATEWAY_URL%/health >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: API Gateway might not be running at %API_GATEWAY_URL%
    echo Please make sure all services are started before running the test
    echo.
    set /p continue="Continue anyway? (y/N): "
    if /i not "%continue%"=="y" (
        echo Load test cancelled
        pause
        exit /b 1
    )
)

REM Rate limiting warnings
if "%GEMINI_TIER%"=="free" (
    echo.
    echo ⚠️  RATE LIMITING WARNINGS:
    echo - Free tier: 15 requests/minute, 1,500 requests/day
    echo - High failure rate expected if users ^> 15
    echo - Consider upgrading to paid tier for production testing
    echo - Paid tier cost: ~$1.65 per assessment
    echo.
)

echo.
echo Starting load test...
echo Press Ctrl+C to stop the test
echo.

REM Run the load test with environment variables
node load-test-100-users.js

REM Check exit code and provide recommendations
if %errorlevel% equ 0 (
    echo.
    echo ✅ Load test completed successfully
    
    if "%GEMINI_TIER%"=="free" (
        echo.
        echo 💡 Free Tier Recommendations:
        echo - If you got good results, consider paid tier for higher loads
        echo - Paid tier allows 1,000 requests/minute vs 15 on free tier
        echo - Cost: ~$165 for 100 assessments
    )
) else (
    echo.
    echo ❌ Load test completed with errors
    
    if "%GEMINI_TIER%"=="free" (
        echo.
        echo 🔧 Free Tier Troubleshooting:
        echo - High failure rate is expected with ^>15 concurrent users
        echo - 429 errors indicate rate limiting
        echo - Solutions:
        echo   1. Reduce concurrent users to 10-15
        echo   2. Add 4000ms+ delay between submissions
        echo   3. Upgrade to paid tier
    ) else (
        echo.
        echo 🔧 Paid Tier Troubleshooting:
        echo - Check if API key is properly configured for paid tier
        echo - Verify billing is enabled in Google Cloud Console
        echo - Check for other system bottlenecks (DB, workers, etc.)
    )
)

echo.
echo Results have been saved to load-test-results-*.json
echo.

REM Show next steps
echo 📋 Next Steps:
echo 1. Review the test results file
echo 2. Check analysis worker logs for processing details
echo 3. Monitor system resources during peak load
if "%GEMINI_TIER%"=="free" (
    echo 4. Consider upgrading to paid tier for production loads
)
echo.

pause
