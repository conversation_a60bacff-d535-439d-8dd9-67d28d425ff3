# Konfigurasi Network dan Infrastructure untuk 100 User dalam 1 Jaringan

## 1. Network Architecture untuk 100 Concurrent Users

### Topology yang Direkomendasikan
```
Internet
    |
[Router/Firewall]
    |
[Load Balancer] (Optional tapi direkomendasikan)
    |
[Switch] - 1Gbps minimum
    |
├── [ATMA Server] - API Gateway, Services
├── [Database Server] - PostgreSQL
├── [Message Queue Server] - RabbitMQ
└── [Client Machines] - 100 users
```

### Network Specifications
```
Network Segment: ***********/24
Available IPs: *********** - ***********54
Server IPs: *********** - ***********0
Client IPs: ***********00 - ***********99
```

## 2. Server Infrastructure Configuration

### ATMA Backend Server
```yaml
Specifications:
  CPU: 8 cores minimum (Intel i7 atau AMD Ryzen 7)
  RAM: 16GB minimum, 32GB recommended
  Storage: 500GB SSD
  Network: 1Gbps Ethernet
  IP: ***********

Services Running:
  - API Gateway (Port 3000)
  - Auth Service (Port 3001)  
  - Assessment Service (Port 3002)
  - Archive Service (Port 3003)
  - Notification Service (Port 3005)
  - Analysis Workers (6 instances)
```

### Database Server (PostgreSQL)
```yaml
Specifications:
  CPU: 4 cores minimum
  RAM: 8GB minimum, 16GB recommended
  Storage: 1TB SSD dengan RAID 1
  Network: 1Gbps Ethernet
  IP: ***********

Configuration:
  max_connections: 200
  shared_buffers: 4GB
  effective_cache_size: 12GB
  work_mem: 16MB
  maintenance_work_mem: 256MB
```

### Message Queue Server (RabbitMQ)
```yaml
Specifications:
  CPU: 4 cores minimum
  RAM: 8GB minimum
  Storage: 200GB SSD
  Network: 1Gbps Ethernet  
  IP: ***********

Configuration:
  vm_memory_high_watermark: 0.6
  disk_free_limit: 2GB
  max_connections: 1000
  heartbeat: 60
```

## 3. Network Configuration

### Router/Gateway Configuration
```bash
# IP: ***********
# Bandwidth: 100Mbps minimum untuk internet
# Internal switching: 1Gbps

# QoS Configuration untuk prioritas traffic
# High Priority: API calls, Database queries
# Medium Priority: File uploads, downloads
# Low Priority: Logging, monitoring

# Port forwarding untuk external access (jika diperlukan)
Port 80 -> ***********:3000   # API Gateway HTTP
Port 443 -> ***********:3000  # API Gateway HTTPS
Port 5432 -> ***********:5432 # Database (admin only)
Port 15672 -> ***********:15672 # RabbitMQ Management
```

### Switch Configuration
```bash
# Managed Switch dengan VLAN support
# Minimum 24 ports Gigabit
# VLAN Configuration:
#   VLAN 10: Servers (***********-10)
#   VLAN 20: Clients (***********00-199)
#   VLAN 30: Management (*************-254)

# Port Configuration:
Ports 1-8: Server VLAN (1Gbps)
Ports 9-24: Client VLAN (1Gbps)
Port 25: Uplink to Router (1Gbps)
```

### DHCP Configuration
```bash
# DHCP Server pada Router
Network: ***********/24
Range: ***********00 - ***********99
Gateway: ***********
DNS: *******, *******
Lease Time: 24 hours

# Static IP Reservations:
*********** - ATMA-SERVER
*********** - DB-SERVER  
*********** - MQ-SERVER
```

## 4. Load Balancer Configuration (Nginx)

### Installation dan Basic Setup
```bash
# Install Nginx
sudo apt update
sudo apt install nginx

# Create configuration
sudo nano /etc/nginx/sites-available/atma-loadbalancer
```

### Nginx Configuration
```nginx
# /etc/nginx/sites-available/atma-loadbalancer

# Rate limiting zones
limit_req_zone $binary_remote_addr zone=login:10m rate=10r/m;
limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;
limit_req_zone $binary_remote_addr zone=submit:10m rate=5r/m;

# Connection limiting
limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;

# Upstream servers
upstream atma_api_gateway {
    least_conn;
    server ***********:3000 max_fails=3 fail_timeout=30s weight=1;
    keepalive 32;
}

# Main server block
server {
    listen 80;
    server_name atma.local ***********;
    
    # Global connection limit
    limit_conn conn_limit_per_ip 20;
    
    # Logging
    access_log /var/log/nginx/atma_access.log;
    error_log /var/log/nginx/atma_error.log;
    
    # Health check endpoint
    location /health {
        proxy_pass http://atma_api_gateway/health;
        access_log off;
    }
    
    # Auth endpoints dengan rate limiting
    location /auth/login {
        limit_req zone=login burst=5 nodelay;
        proxy_pass http://atma_api_gateway;
        include /etc/nginx/proxy_params;
    }
    
    # Assessment submit dengan rate limiting
    location /assessments/submit {
        limit_req zone=submit burst=10 nodelay;
        proxy_pass http://atma_api_gateway;
        include /etc/nginx/proxy_params;
        
        # Increase timeouts untuk assessment processing
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # General API endpoints
    location / {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://atma_api_gateway;
        include /etc/nginx/proxy_params;
    }
}

# Proxy parameters
# /etc/nginx/proxy_params
proxy_set_header Host $http_host;
proxy_set_header X-Real-IP $remote_addr;
proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
proxy_set_header X-Forwarded-Proto $scheme;
proxy_buffering on;
proxy_buffer_size 128k;
proxy_buffers 4 256k;
proxy_busy_buffers_size 256k;
```

## 5. Client Machine Configuration

### Minimum Client Specifications
```yaml
Per Client Machine:
  CPU: 2 cores minimum
  RAM: 4GB minimum
  Network: 100Mbps Ethernet
  OS: Windows 10/11, macOS, atau Linux

Browser Requirements:
  Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
  JavaScript enabled
  Local Storage support
```

### Network Configuration per Client
```bash
# Static IP atau DHCP
IP Range: ***********00 - ***********99
Subnet Mask: *************
Gateway: ***********
DNS: *******, *******

# Hosts file entry (optional)
*********** atma.local
*********** atma-api.local
```

## 6. Bandwidth dan Performance Planning

### Bandwidth Requirements
```
Per User Concurrent Load:
  Login Request: ~2KB
  Assessment Submit: ~10KB
  Response Data: ~5KB
  Total per User: ~17KB per session

100 Concurrent Users:
  Peak Bandwidth: 100 × 17KB = 1.7MB
  Sustained Load: ~500KB/s
  Recommended Bandwidth: 10Mbps minimum
```

### Network Performance Optimization
```bash
# TCP optimization pada server
echo 'net.core.somaxconn = 1024' >> /etc/sysctl.conf
echo 'net.core.netdev_max_backlog = 5000' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_max_syn_backlog = 1024' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_keepalive_time = 600' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_keepalive_intvl = 60' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_keepalive_probes = 3' >> /etc/sysctl.conf
echo 'net.ipv4.tcp_fin_timeout = 30' >> /etc/sysctl.conf

# Apply changes
sysctl -p
```

## 7. Monitoring dan Troubleshooting

### Network Monitoring Tools
```bash
# Bandwidth monitoring
sudo apt install iftop nethogs
iftop -i eth0
nethogs eth0

# Connection monitoring  
netstat -an | grep :3000 | wc -l
ss -tuln | grep :3000

# Latency testing
ping -c 10 ***********
traceroute ***********
```

### Performance Testing Commands
```bash
# Test concurrent connections
ab -n 1000 -c 100 http://***********/health

# Test API endpoints
curl -w "@curl-format.txt" -o /dev/null -s http://***********/health

# Monitor server resources
htop
iostat -x 1
vmstat 1
```

### Troubleshooting Common Issues

#### 1. High Latency
```bash
# Check network path
traceroute ***********
mtr ***********

# Check switch performance
ethtool eth0
```

#### 2. Connection Timeouts
```bash
# Check connection limits
ulimit -n
cat /proc/sys/net/core/somaxconn

# Check server load
uptime
top
```

#### 3. Bandwidth Saturation
```bash
# Monitor bandwidth usage
iftop -B -i eth0
vnstat -i eth0

# Check for network errors
ethtool -S eth0 | grep error
```

## 8. Security Configuration

### Firewall Rules (iptables)
```bash
# Allow internal network traffic
iptables -A INPUT -s ***********/24 -j ACCEPT

# Allow specific ports
iptables -A INPUT -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j ACCEPT
iptables -A INPUT -p tcp --dport 3000 -j ACCEPT

# Rate limiting
iptables -A INPUT -p tcp --dport 80 -m limit --limit 100/min -j ACCEPT
iptables -A INPUT -p tcp --dport 80 -j DROP

# Save rules
iptables-save > /etc/iptables/rules.v4
```

### Network Segmentation
```bash
# VLAN Configuration untuk security
VLAN 10: Server Network (***********-10)
VLAN 20: Client Network (***********00-199)  
VLAN 30: Management Network (*************-254)

# Inter-VLAN routing rules
Allow: VLAN 20 -> VLAN 10 (ports 80, 443, 3000)
Deny: VLAN 20 -> VLAN 10 (ports 5432, 15672)
Allow: VLAN 30 -> All (management access)
```
