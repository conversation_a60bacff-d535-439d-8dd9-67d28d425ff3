# Rate Limiting Fixes untuk 100 Concurrent Users

## 🚨 Masalah Rate Limiting yang Ditemukan

### 1. API Gateway Rate Limiting
**Current Configuration:**
```javascript
// Assessment Rate Limit
windowMs: 60 * 60 * 1000, // 1 hour
max: 500, // per user per hour (sudah cukup)

// Auth Rate Limit  
windowMs: 15 * 60 * 1000, // 15 minutes
max: 200, // per IP (sudah cukup)

// General Rate Limit
windowMs: 15 * 60 * 1000, // 15 minutes  
max: 200, // per IP (sudah cukup)
```

**Status**: ✅ **API Gateway rate limits sudah cukup untuk 100 users**

### 2. Gemini API Rate Limiting (KRITIS)
**Free Tier Limits:**
- 15 requests per minute
- 1,500 requests per day
- 1 million tokens per minute

**Problem untuk 100 concurrent users:**
- 100 concurrent requests akan langsung exceed 15 RPM limit
- Akan menyebabkan 429 errors dan failed assessments

## 🔧 Solutions Required

### Solution 1: Upgrade Gemini API Tier (RECOMMENDED)

#### Gemini API Pricing Tiers:
```
Free Tier:
- 15 requests/minute
- 1,500 requests/day
- $0/month
❌ TIDAK CUKUP untuk 100 concurrent users

Pay-as-you-go:
- 1,000 requests/minute  
- No daily limit
- $0.075 per 1K characters input
- $0.30 per 1K characters output
✅ CUKUP untuk 100 concurrent users

Provisioned Throughput:
- Custom limits
- Guaranteed capacity
- Higher cost but predictable
✅ OPTIMAL untuk production load
```

#### Recommended Action:
1. **Upgrade ke Pay-as-you-go tier**
2. **Set up billing di Google AI Studio**
3. **Monitor usage dan costs**

### Solution 2: Implement Request Queuing & Batching

#### Rate Limiting Handler di Analysis Worker
```javascript
// analysis-worker/src/services/aiService.js

const GEMINI_RATE_LIMIT = {
  requestsPerMinute: 15, // Free tier limit
  requestsPerDay: 1500,
  tokensPerMinute: 1000000
};

class RateLimitedAIService {
  constructor() {
    this.requestQueue = [];
    this.requestCount = 0;
    this.lastMinute = Date.now();
    this.dailyRequestCount = 0;
    this.lastDay = Date.now();
  }

  async processWithRateLimit(assessmentData) {
    // Check rate limits
    const now = Date.now();
    
    // Reset counters if needed
    if (now - this.lastMinute > 60000) {
      this.requestCount = 0;
      this.lastMinute = now;
    }
    
    if (now - this.lastDay > 86400000) {
      this.dailyRequestCount = 0;
      this.lastDay = now;
    }
    
    // Check if we can make request
    if (this.requestCount >= GEMINI_RATE_LIMIT.requestsPerMinute) {
      // Wait until next minute
      const waitTime = 60000 - (now - this.lastMinute);
      await new Promise(resolve => setTimeout(resolve, waitTime));
      return this.processWithRateLimit(assessmentData);
    }
    
    if (this.dailyRequestCount >= GEMINI_RATE_LIMIT.requestsPerDay) {
      throw new Error('Daily rate limit exceeded');
    }
    
    // Make request
    this.requestCount++;
    this.dailyRequestCount++;
    
    return await this.callGeminiAPI(assessmentData);
  }
}
```

### Solution 3: Multiple API Keys (Load Distribution)

#### Environment Configuration:
```bash
# Multiple Gemini API Keys untuk load distribution
GOOGLE_AI_API_KEY_1=key1_here
GOOGLE_AI_API_KEY_2=key2_here  
GOOGLE_AI_API_KEY_3=key3_here
GOOGLE_AI_API_KEY_4=key4_here

# Key rotation strategy
API_KEY_ROTATION_STRATEGY=round_robin
```

#### Implementation:
```javascript
// analysis-worker/src/config/ai.js

const API_KEYS = [
  process.env.GOOGLE_AI_API_KEY_1,
  process.env.GOOGLE_AI_API_KEY_2,
  process.env.GOOGLE_AI_API_KEY_3,
  process.env.GOOGLE_AI_API_KEY_4
].filter(key => key); // Remove undefined keys

let currentKeyIndex = 0;

const getNextAPIKey = () => {
  const key = API_KEYS[currentKeyIndex];
  currentKeyIndex = (currentKeyIndex + 1) % API_KEYS.length;
  return key;
};

const createAIClient = () => {
  const apiKey = getNextAPIKey();
  return new GoogleGenAI({ apiKey });
};
```

## 🚀 Immediate Actions Required

### 1. Upgrade Gemini API Tier (CRITICAL)
```bash
# Go to Google AI Studio
# https://aistudio.google.com/

# Steps:
1. Login to Google AI Studio
2. Go to "Get API Key" section
3. Enable billing for your project
4. Upgrade to Pay-as-you-go tier
5. Set spending limits (recommended: $50/month for testing)
6. Update API key in environment variables
```

### 2. Update Load Test Configuration
```bash
# Untuk testing dengan free tier (limited)
CONCURRENT_USERS=10  # Reduced untuk free tier
ASSESSMENT_DELAY=4000  # 4 second delay between requests

# Untuk testing dengan paid tier
CONCURRENT_USERS=100
ASSESSMENT_DELAY=0  # No delay needed
```

### 3. Monitor API Usage
```javascript
// Add to analysis-worker
const usage = {
  requestsThisMinute: 0,
  requestsToday: 0,
  tokensUsed: 0,
  estimatedCost: 0
};

// Log usage after each request
logger.info('Gemini API usage', {
  requestsThisMinute: usage.requestsThisMinute,
  requestsToday: usage.requestsToday,
  estimatedCost: usage.estimatedCost
});
```

## 💰 Cost Estimation untuk 100 Users

### Gemini API Costs (Pay-as-you-go):
```
Input: ~2,000 characters per assessment
Output: ~5,000 characters per assessment

Cost per assessment:
- Input: 2K chars × $0.075/1K = $0.15
- Output: 5K chars × $0.30/1K = $1.50
- Total: ~$1.65 per assessment

100 assessments: ~$165
Daily testing (3 runs): ~$495
Monthly testing: ~$15,000
```

### Recommendations:
1. **Start dengan Pay-as-you-go** untuk testing
2. **Set spending alerts** di Google Cloud Console
3. **Consider Provisioned Throughput** untuk production
4. **Optimize prompts** untuk reduce token usage

## 🔧 Implementation Files to Update

### 1. API Gateway Rate Limits (Optional - sudah cukup)
```javascript
// api-gateway/src/middleware/rateLimiter.js
// Current limits sudah cukup, no changes needed
```

### 2. Analysis Worker AI Service
```javascript
// analysis-worker/src/services/aiService.js
// Add rate limiting logic
// Add multiple API key support
// Add usage monitoring
```

### 3. Environment Variables
```bash
# analysis-worker/.env
GOOGLE_AI_API_KEY=your_paid_tier_api_key_here
GOOGLE_AI_TIER=paid  # or 'free' for testing

# Optional: Multiple keys
GOOGLE_AI_API_KEY_1=key1
GOOGLE_AI_API_KEY_2=key2
```

### 4. Load Test Configuration
```bash
# For paid tier testing
CONCURRENT_USERS=100
ASSESSMENT_DELAY=0
GEMINI_TIER=paid

# For free tier testing  
CONCURRENT_USERS=10
ASSESSMENT_DELAY=4000
GEMINI_TIER=free
```

## 📊 Testing Strategy

### Phase 1: Free Tier Testing (Limited)
```bash
# Test dengan 10 users, 4 second delays
CONCURRENT_USERS=10 ASSESSMENT_DELAY=4000 node load-test-100-users.js
```

### Phase 2: Paid Tier Testing (Full Load)
```bash
# Test dengan 100 users, no delays
CONCURRENT_USERS=100 ASSESSMENT_DELAY=0 node load-test-100-users.js
```

### Phase 3: Production Simulation
```bash
# Multiple test runs
for i in {1..5}; do
  CONCURRENT_USERS=100 node load-test-100-users.js
  sleep 300  # 5 minute break
done
```

## 🚨 Critical Next Steps

1. **IMMEDIATE**: Upgrade Gemini API ke paid tier
2. **URGENT**: Set spending limits dan alerts
3. **IMPORTANT**: Update load test untuk handle rate limits
4. **RECOMMENDED**: Implement usage monitoring
5. **OPTIONAL**: Add multiple API key support

**Without upgrading Gemini API tier, 100 concurrent users WILL FAIL due to rate limits!**
