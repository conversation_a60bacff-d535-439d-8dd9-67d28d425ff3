# RabbitMQ Configuration untuk 100 Concurrent Users
# File: /etc/rabbitmq/rabbitmq.conf

## Network Configuration
listeners.tcp.default = 5672
num_acceptors.tcp = 20
handshake_timeout = 10000
heartbeat = 60

## Memory Management
# Set memory threshold to 60% of available RAM
vm_memory_high_watermark.relative = 0.6
vm_memory_high_watermark_paging_ratio = 0.5

# Disk space threshold
disk_free_limit.relative = 2.0

## Connection Limits
# Maximum number of concurrent connections
max_connections = 1000

# Connection timeout
tcp_listen_options.backlog = 128
tcp_listen_options.nodelay = true
tcp_listen_options.keepalive = true

## Queue Configuration
# Default queue arguments
default_vhost = /
default_user = guest
default_pass = guest

# Queue master locator
queue_master_locator = min-masters

## Performance Tuning
# Channel limits
channel_max = 2047

# Message size limits
max_message_size = 134217728  # 128MB

# Prefetch settings (will be overridden by client)
consumer_prefetch = 250

## Clustering (if using multiple RabbitMQ nodes)
cluster_formation.peer_discovery_backend = rabbit_peer_discovery_classic_config
cluster_formation.classic_config.nodes.1 = rabbit@node1
cluster_formation.classic_config.nodes.2 = rabbit@node2

## Logging
log.console = true
log.console.level = info
log.file = /var/log/rabbitmq/rabbit.log
log.file.level = info
log.file.rotation.date = $D0
log.file.rotation.size = 10485760  # 10MB

## Management Plugin
management.tcp.port = 15672
management.tcp.ip = 0.0.0.0
management.http_log_dir = /var/log/rabbitmq/management

## SSL (if needed)
# ssl_options.cacertfile = /path/to/ca_certificate.pem
# ssl_options.certfile = /path/to/server_certificate.pem
# ssl_options.keyfile = /path/to/server_key.pem
# ssl_options.verify = verify_peer
# ssl_options.fail_if_no_peer_cert = true

## High Availability
# Enable HA policies
# ha-mode = all
# ha-sync-mode = automatic
