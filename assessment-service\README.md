# Assessment Service

Service untuk menerima data assessment dari frontend dan mempublikasikannya ke Message Queue untuk diproses oleh Analysis Worker.

## Fungsi Utama

1. **Data Reception**: Menerima data assessment dalam format JSON
2. **Primary Validation**: Validasi lengkap dan komprehensif data assessment (single source of truth)
3. **Token Management**: Verifikasi dan mengurangi saldo token user
4. **Queue Publishing**: Mempublikasikan job ke RabbitMQ untuk diproses
5. **Job Tracking**: Memberikan job ID untuk tracking status

## Validation Strategy

**Assessment Service** adalah **single source of truth** untuk validasi data assessment:
- ✅ **Full validation** dilakukan di sini untuk semua assessment data
- ✅ **Downstream services** (Analysis Worker, Archive Service) hanya melakukan validasi minimal
- ✅ **Trust internal data** - data yang sudah divalidasi di sini dipercaya oleh service lain
- ✅ **Performance optimization** - menghindari redundant validation di multiple services

## Port
- **Development**: 3003
- **Production**: 3003

## ✅ Implementation Status

**COMPLETED** - Assessment Service telah diimplementasikan dengan fitur lengkap:

- ✅ Express.js server dengan middleware lengkap
- ✅ JWT authentication dan token management
- ✅ Comprehensive validation untuk semua assessment components
- ✅ RabbitMQ integration untuk job queuing
- ✅ In-memory job tracking system
- ✅ Health check endpoints
- ✅ Error handling dan logging
- ✅ Basic unit tests
- ✅ API documentation

## Project Structure

```
assessment-service/
├── src/
│   ├── app.js                    # Main application server
│   ├── config/
│   │   ├── database.js           # Database configuration (placeholder)
│   │   └── rabbitmq.js           # RabbitMQ connection and setup
│   ├── middleware/
│   │   ├── auth.js               # JWT authentication middleware
│   │   ├── errorHandler.js       # Global error handler
│   │   └── validation.js         # Request validation middleware
│   ├── routes/
│   │   ├── assessments.js        # Assessment endpoints
│   │   └── health.js             # Health check endpoints
│   ├── services/
│   │   ├── authService.js        # Auth service integration
│   │   └── queueService.js       # RabbitMQ queue operations
│   ├── utils/
│   │   ├── logger.js             # Logging utility
│   │   └── responseHelper.js     # Response formatting
│   ├── schemas/
│   │   └── assessment.js         # Assessment validation schemas
│   └── jobs/
│       └── jobTracker.js         # In-memory job status tracking
├── tests/
│   ├── app.test.js               # Application tests
│   └── schemas.test.js           # Schema validation tests
├── .env.example                  # Environment variables template
├── package.json                  # Dependencies and scripts
└── README.md                     # Documentation
```

## Dependencies

```json
{
  "express": "^4.18.2",
  "amqplib": "^0.10.3",
  "joi": "^17.9.1",
  "express-jwt": "^8.4.1",
  "cors": "^2.8.5",
  "dotenv": "^16.0.3",
  "axios": "^1.4.0",
  "uuid": "^9.0.0",
  "morgan": "^1.10.0"
}
```

## Environment Variables

```env
# Server Configuration
PORT=3003
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production

# RabbitMQ Configuration
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
QUEUE_NAME=assessment_analysis
EXCHANGE_NAME=atma_exchange
ROUTING_KEY=analysis.process

# Auth Service Configuration
AUTH_SERVICE_URL=http://localhost:3001

# Token Cost Configuration
ANALYSIS_TOKEN_COST=1

# Queue Configuration
QUEUE_DURABLE=true
MESSAGE_PERSISTENT=true

# Database Configuration (Placeholder)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=atma_db
DB_USER=postgres
DB_PASSWORD=password
DB_DIALECT=postgres
DB_SCHEMA=assessment

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/assessment-service.log

# Internal Service Configuration
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
```

## Message Queue Setup

### RabbitMQ Exchange dan Queue
```javascript
// Exchange: atma_exchange (type: direct)
// Queue: assessment_analysis
// Routing Key: analysis.process
```

## API Endpoints

### POST /assessments/submit
**Description:** Submit assessment data for AI analysis
**Authentication:** Required (JWT Bearer token)
**Rate Limit:** 10 requests per hour per user (handled by API Gateway)

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "riasec": {
    "realistic": 75,
    "investigative": 85,
    "artistic": 60,
    "social": 50,
    "enterprising": 70,
    "conventional": 55
  },
  "ocean": {
    "openness": 80,
    "conscientiousness": 65,
    "extraversion": 55,
    "agreeableness": 45,
    "neuroticism": 30
  },
  "viaIs": {
    "creativity": 85,
    "curiosity": 78,
    "judgment": 70,
    "loveOfLearning": 82,
    "perspective": 60,
    "bravery": 55,
    "perseverance": 68,
    "honesty": 73,
    "zest": 66,
    "love": 80,
    "kindness": 75,
    "socialIntelligence": 65,
    "teamwork": 60,
    "fairness": 70,
    "leadership": 67,
    "forgiveness": 58,
    "humility": 62,
    "prudence": 69,
    "selfRegulation": 61,
    "appreciationOfBeauty": 50,
    "gratitude": 72,
    "hope": 77,
    "humor": 65,
    "spirituality": 55
  }
}
```

**Response Success (202):**
```json
{
  "success": true,
  "message": "Assessment submitted successfully and queued for analysis",
  "data": {
    "jobId": "550e8400-e29b-41d4-a716-446655440000",
    "status": "queued",
    "estimatedProcessingTime": "2-5 minutes",
    "queuePosition": 3,
    "tokenCost": 1,
    "remainingTokens": 4
  }
}
```

**Response Error (400) - Validation Error:**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Validation failed",
    "details": {
      "riasec.realistic": "Realistic score must be a number between 0 and 100"
    }
  }
}
```

**Response Error (402) - Insufficient Tokens:**
```json
{
  "success": false,
  "error": {
    "code": "INSUFFICIENT_TOKENS",
    "message": "Insufficient token balance. Required: 1, Available: 0",
    "details": {
      "required": 1,
      "available": 0
    }
  }
}
```

### Job Message Format
```json
{
  "jobId": "550e8400-e29b-41d4-a716-446655440000",
  "userId": "550e8400-e29b-41d4-a716-446655440001",
  "assessmentData": {
    "riasec": {
      "realistic": 75,
      "investigative": 85,
      "artistic": 60,
      "social": 50,
      "enterprising": 70,
      "conventional": 55
    },
    "ocean": {
      "openness": 80,
      "conscientiousness": 65,
      "extraversion": 55,
      "agreeableness": 45,
      "neuroticism": 30
    },
    "viaIs": {
      "creativity": 85,
      "curiosity": 78,
      "judgment": 70,
      "loveOfLearning": 82,
      "perspective": 60,
      "bravery": 55,
      "perseverance": 68,
      "honesty": 73,
      "zest": 66,
      "love": 80,
      "kindness": 75,
      "socialIntelligence": 65,
      "teamwork": 60,
      "fairness": 70,
      "leadership": 67,
      "forgiveness": 58,
      "humility": 62,
      "prudence": 69,
      "selfRegulation": 61,
      "appreciationOfBeauty": 50,
      "gratitude": 72,
      "hope": 77,
      "humor": 65,
      "spirituality": 55
    },

  },
  "timestamp": "2024-01-01T00:00:00.000Z",
  "priority": 1
}
```

## API Endpoints

### POST /assessments/submit
Submit data assessment untuk dianalisis

**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

**Request Body:**
```json
{
  "riasec": {
    "realistic": 75,
    "investigative": 85,
    "artistic": 60,
    "social": 50,
    "enterprising": 70,
    "conventional": 55
  },
  "ocean": {
    "openness": 80,
    "conscientiousness": 65,
    "extraversion": 55,
    "agreeableness": 45,
    "neuroticism": 30
  },
  "viaIs": {
    "creativity": 85,
    "curiosity": 78,
    "judgment": 70,
    "loveOfLearning": 82,
    "perspective": 60,
    "bravery": 55,
    "perseverance": 68,
    "honesty": 73,
    "zest": 66,
    "love": 80,
    "kindness": 75,
    "socialIntelligence": 65,
    "teamwork": 60,
    "fairness": 70,
    "leadership": 67,
    "forgiveness": 58,
    "humility": 62,
    "prudence": 69,
    "selfRegulation": 61,
    "appreciationOfBeauty": 50,
    "gratitude": 72,
    "hope": 77,
    "humor": 65,
    "spirituality": 55
  },

}
```

### GET /assessments/status/:jobId
**Description:** Check assessment processing status
**Authentication:** Required (JWT Bearer token)

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response Success (200):**
```json
{
  "success": true,
  "message": "Job status retrieved successfully",
  "data": {
    "jobId": "550e8400-e29b-41d4-a716-446655440000",
    "status": "processing",
    "progress": 75,
    "estimatedTimeRemaining": "1-2 minutes",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:02:00Z"
  }
}
```

**Job Status Values:**
- `queued`: Job dalam antrian
- `processing`: Sedang diproses oleh worker
- `completed`: Analisis selesai, hasil tersedia di Archive Service
- `failed`: Analisis gagal

### GET /assessments/queue/status
**Description:** Get queue status (for monitoring)
**Authentication:** Required (JWT Bearer token)

**Response Success (200):**
```json
{
  "success": true,
  "message": "Queue status retrieved successfully",
  "data": {
    "queueLength": 5,
    "activeWorkers": 2,
    "averageProcessingTime": "3.2 minutes",
    "estimatedWaitTime": "5-10 minutes",
    "jobStats": {
      "total": 15,
      "queued": 5,
      "processing": 2,
      "completed": 7,
      "failed": 1
    }
  }
}
```

### Health Check Endpoints

#### GET /health
**Description:** Comprehensive health check
**Authentication:** Not required

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "service": "assessment-service",
  "version": "1.0.0",
  "dependencies": {
    "rabbitmq": {
      "status": "healthy",
      "details": {
        "queueName": "assessment_analysis",
        "messageCount": 5,
        "consumerCount": 2,
        "isHealthy": true
      }
    },
    "authService": {
      "status": "healthy"
    }
  },
  "jobs": {
    "total": 15,
    "queued": 5,
    "processing": 2,
    "completed": 7,
    "failed": 1
  },
  "system": {
    "uptime": 3600,
    "memory": {...},
    "hostname": "assessment-service-1"
  }
}
```

#### GET /health/live
**Description:** Simple liveness probe
**Authentication:** Not required

#### GET /health/ready
**Description:** Readiness probe
**Authentication:** Not required

#### GET /health/queue
**Description:** Queue health check
**Authentication:** Not required

## Validation Rules

**Assessment Data Requirements (PRIMARY VALIDATION):**
- ✅ **Comprehensive validation** - Assessment Service melakukan validasi lengkap
- ✅ **Single source of truth** - Hanya di sini validasi detail dilakukan
- ✅ **Downstream trust** - Service lain mempercayai data yang sudah divalidasi

**Validation Details:**
- Semua kategori assessment harus ada (riasec, ocean, viaIs)
- Nilai harus berupa integer antara 0-100
- RIASEC: 6 dimensi wajib
- OCEAN: 5 dimensi wajib
- VIA-IS: 24 character strengths wajib

**Validation Architecture:**
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  Assessment     │    │  Analysis        │    │  Archive        │
│  Service        │    │  Worker          │    │  Service        │
│                 │    │                  │    │                 │
│ ✅ FULL         │───▶│ ✅ MESSAGE       │───▶│ ✅ MINIMAL      │
│ VALIDATION      │    │ STRUCTURE ONLY   │    │ VALIDATION      │
│                 │    │                  │    │                 │
│ • RIASEC        │    │ • jobId          │    │ • persona       │
│ • OCEAN         │    │ • userId         │    │   profile       │
│ • VIA-IS        │    │ • userEmail      │    │ • status        │
│ • All fields    │    │ • assessmentData │    │                 │
│                 │    │   (as object)    │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

**Response Success (202):**
```json
{
  "success": true,
  "message": "Assessment submitted successfully and queued for analysis",
  "data": {
    "jobId": "550e8400-e29b-41d4-a716-446655440000",
    "status": "queued",
    "estimatedProcessingTime": "2-5 minutes",
    "queuePosition": 3,
    "tokenCost": 1,
    "remainingTokens": 4
  }
}
```

**Response Error (400) - Validation Error:**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid assessment data",
    "details": [
      "riasec.realistic must be between 0 and 100",
      "viaIs.creativity is required"
    ]
  }
}
```

**Response Error (402) - Insufficient Tokens:**
```json
{
  "success": false,
  "error": {
    "code": "INSUFFICIENT_TOKENS",
    "message": "Insufficient token balance for analysis",
    "data": {
      "required": 1,
      "available": 0
    }
  }
}
```

### GET /assessments/job/:jobId
Mendapatkan status job assessment

**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response Success (200):**
```json
{
  "success": true,
  "data": {
    "jobId": "550e8400-e29b-41d4-a716-446655440000",
    "status": "processing",
    "submittedAt": "2024-01-01T00:00:00.000Z",
    "estimatedCompletion": "2024-01-01T00:03:00.000Z",
    "queuePosition": 1
  }
}
```

**Job Status Values:**
- `queued`: Job dalam antrian
- `processing`: Sedang diproses oleh worker
- `completed`: Analisis selesai, hasil tersedia di Archive Service
- `failed`: Analisis gagal

### GET /assessments/queue/status
Mendapatkan status antrian (untuk monitoring)

**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response Success (200):**
```json
{
  "success": true,
  "data": {
    "queueLength": 5,
    "activeWorkers": 2,
    "averageProcessingTime": "3.2 minutes",
    "estimatedWaitTime": "6-8 minutes"
  }
}
```

### POST /assessments/validate
Validasi data assessment tanpa submit (untuk preview)

**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

**Request Body:** (sama dengan /submit)

**Response Success (200):**
```json
{
  "success": true,
  "message": "Assessment data is valid",
  "data": {
    "valid": true,
    "completeness": {
      "riasec": true,
      "ocean": true,
      "viaIs": true,
      "multipleIntelligences": true,
      "cognitiveStyleIndex": true
    },
    "tokenCost": 1
  }
}
```

## Health Check

### GET /health
**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "rabbitmq": "connected",
  "queue": "assessment_analysis",
  "version": "1.0.0"
}
```

## Error Codes

- `VALIDATION_ERROR`: Data assessment tidak valid
- `INSUFFICIENT_TOKENS`: Saldo token tidak mencukupi
- `QUEUE_ERROR`: Error saat publish ke message queue
- `JOB_NOT_FOUND`: Job ID tidak ditemukan
- `UNAUTHORIZED`: Token tidak valid atau expired
- `FORBIDDEN`: User tidak memiliki akses ke resource
- `AUTH_SERVICE_UNAVAILABLE`: Auth service tidak dapat diakses
- `TOKEN_DEDUCTION_FAILED`: Gagal mengurangi token balance
- `INTERNAL_ERROR`: Server error

## Integration Points

### With API Gateway
- Receives proxied requests dengan user context headers
- Returns standardized JSON responses

### With Auth Service
- Token verification requests
- Token balance check dan deduction

### With RabbitMQ
- Publishes assessment jobs ke analysis queue
- Handles connection failures dan retries

### With Analysis Worker (Indirect)
- Provides job data melalui RabbitMQ messages
- Job status tracking untuk user feedback

## Development & Testing

### Local Development
```bash
# Install dependencies
npm install

# Setup environment
cp .env.example .env
# Edit .env dengan konfigurasi yang sesuai

# Start service in development mode
npm run dev
```

### Testing
```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch
```

### Linting
```bash
# Run linter
npm run lint

# Fix linting issues
npm run lint:fix
```

## Data Validation Schema

### RIASEC (6 dimensi)
```javascript
{
  realistic: Joi.number().integer().min(0).max(100).required(),
  investigative: Joi.number().integer().min(0).max(100).required(),
  artistic: Joi.number().integer().min(0).max(100).required(),
  social: Joi.number().integer().min(0).max(100).required(),
  enterprising: Joi.number().integer().min(0).max(100).required(),
  conventional: Joi.number().integer().min(0).max(100).required()
}
```

### OCEAN (5 dimensi)
```javascript
{
  openness: Joi.number().integer().min(0).max(100).required(),
  conscientiousness: Joi.number().integer().min(0).max(100).required(),
  extraversion: Joi.number().integer().min(0).max(100).required(),
  agreeableness: Joi.number().integer().min(0).max(100).required(),
  neuroticism: Joi.number().integer().min(0).max(100).required()
}
```

### VIA-IS (24 character strengths)
```javascript
{
  creativity: Joi.number().integer().min(0).max(100).required(),
  curiosity: Joi.number().integer().min(0).max(100).required(),
  judgment: Joi.number().integer().min(0).max(100).required(),
  loveOfLearning: Joi.number().integer().min(0).max(100).required(),
  perspective: Joi.number().integer().min(0).max(100).required(),
  bravery: Joi.number().integer().min(0).max(100).required(),
  perseverance: Joi.number().integer().min(0).max(100).required(),
  honesty: Joi.number().integer().min(0).max(100).required(),
  zest: Joi.number().integer().min(0).max(100).required(),
  love: Joi.number().integer().min(0).max(100).required(),
  kindness: Joi.number().integer().min(0).max(100).required(),
  socialIntelligence: Joi.number().integer().min(0).max(100).required(),
  teamwork: Joi.number().integer().min(0).max(100).required(),
  fairness: Joi.number().integer().min(0).max(100).required(),
  leadership: Joi.number().integer().min(0).max(100).required(),
  forgiveness: Joi.number().integer().min(0).max(100).required(),
  humility: Joi.number().integer().min(0).max(100).required(),
  prudence: Joi.number().integer().min(0).max(100).required(),
  selfRegulation: Joi.number().integer().min(0).max(100).required(),
  appreciationOfBeauty: Joi.number().integer().min(0).max(100).required(),
  gratitude: Joi.number().integer().min(0).max(100).required(),
  hope: Joi.number().integer().min(0).max(100).required(),
  humor: Joi.number().integer().min(0).max(100).required(),
  spirituality: Joi.number().integer().min(0).max(100).required()
}
```

## Security Features

1. **JWT Authentication**: Verifikasi token untuk setiap request
2. **Token Deduction**: Otomatis mengurangi saldo token setelah submit
3. **Input Validation**: Comprehensive validation menggunakan Joi
4. **Rate Limiting**: Implementasi di API Gateway
5. **Queue Security**: Message queue dengan authentication
