@echo off
REM Load Test Runner for Windows
REM Script untuk menjalankan load test 100 concurrent users

echo ========================================
echo ATMA Backend Load Test - 100 Users
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

REM Check if required packages are installed
if not exist "node_modules\axios" (
    echo Installing required packages...
    npm install axios uuid
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install packages
        pause
        exit /b 1
    )
)

REM Set environment variables for load test
set API_GATEWAY_URL=http://localhost:3000
set CONCURRENT_USERS=100
set ASSESSMENT_DELAY=0

echo Configuration:
echo - API Gateway URL: %API_GATEWAY_URL%
echo - Concurrent Users: %CONCURRENT_USERS%
echo - Assessment Delay: %ASSESSMENT_DELAY%ms
echo.

REM Check if services are running
echo Checking if services are running...
curl -s %API_GATEWAY_URL%/health >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: API Gateway might not be running at %API_GATEWAY_URL%
    echo Please make sure all services are started before running the test
    echo.
    set /p continue="Continue anyway? (y/N): "
    if /i not "%continue%"=="y" (
        echo Load test cancelled
        pause
        exit /b 1
    )
)

echo.
echo Starting load test...
echo Press Ctrl+C to stop the test
echo.

REM Run the load test
node load-test-100-users.js

REM Check exit code
if %errorlevel% equ 0 (
    echo.
    echo ✅ Load test completed successfully
) else (
    echo.
    echo ❌ Load test completed with errors
)

echo.
echo Results have been saved to load-test-results-*.json
echo.
pause
