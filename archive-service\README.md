# Archive Service

Service untuk menyimpan dan men<PERSON><PERSON> hasil analisis persona yang telah selesai diproses oleh Analysis Worker.

## ✅ Implementation Status

**COMPLETED** - Archive Service telah sepenuhnya diimplementasikan dan terintegrasi dengan:
- ✅ API Gateway (proxy routes)
- ✅ Assessment Service (data flow)
- ✅ Analysis Worker (result storage)
- ✅ PostgreSQL Database (archive schema)

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL database dengan schema `archive`
- Environment variables dikonfigurasi

### Installation & Setup
```bash
# 1. Install dependencies
npm install

# 2. Copy environment file
cp .env.example .env

# 3. Configure database connection in .env
# Edit DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASSWORD

# 4. Run database initialization script (if not done)
# psql -U postgres -d atma_db -f ../scripts/init-databases.sql

# 5. Start development server
npm run dev
```

### Verify Installation
```bash
# Check service health
curl http://localhost:3002/health

# Check service info
curl http://localhost:3002/
```

### Database Testing
Untuk memverifikasi koneksi database dan struktur tabel:

```bash
# Test koneksi database dan cek schema/table
npm run test:db
```

Script ini akan:
1. Menguji koneksi ke database PostgreSQL
2. Memeriksa keberadaan schema `archive`
3. Memeriksa struktur tabel `analysis_results`
4. Memeriksa indeks yang ada
5. Memeriksa data sampel (jika ada)

Jika ada masalah, script akan memberikan saran untuk memperbaikinya.

### API Testing
Untuk menguji API endpoints dan integrasi:

```bash
# Start service terlebih dahulu
npm run dev

# Dalam terminal terpisah, jalankan test API
npm run test:api
```

Script ini akan menguji:
1. Health check endpoint
2. Root endpoint
3. Internal service authentication
4. Create result endpoint
5. Get results endpoint
6. Get result by ID endpoint
7. Statistics endpoint
8. Update result endpoint

Script ini mensimulasikan request dari API Gateway dan Analysis Worker.

## Fungsi Utama

1. **Storage Management**: Menyimpan hasil analisis persona dalam format JSONB
2. **CRUD Operations**: Create, Read, Update, Delete untuk analysis results
3. **User-based Access**: Setiap user hanya dapat mengakses hasil analisisnya sendiri
4. **Pagination**: Support pagination untuk list results
5. **Search & Filter**: Pencarian dan filter berdasarkan status, tanggal, dll
6. **Minimal Validation**: Validasi minimal untuk data yang sudah divalidasi upstream

## Validation Strategy

**Archive Service** menggunakan **trust-based validation approach**:
- ✅ **Minimal validation** - Hanya validasi business rules dan data integrity
- ✅ **Trust assessment data** - Data assessment sudah divalidasi di Assessment Service
- ✅ **Focus on storage** - Optimized untuk storage dan retrieval operations
- ✅ **Persona profile validation** - Validasi output AI untuk data consistency

## Port
- **Development**: 3002
- **Production**: 3002

## Database Schema

```sql
-- Schema: archive
CREATE TABLE archive.analysis_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL,
    assessment_data JSONB,
    persona_profile JSONB NOT NULL,
    status VARCHAR(50) DEFAULT 'completed',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Foreign key constraint to auth schema
ALTER TABLE archive.analysis_results
ADD CONSTRAINT fk_analysis_results_user_id
FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Indexes for better performance
CREATE INDEX idx_analysis_results_user_id ON archive.analysis_results(user_id);
CREATE INDEX idx_analysis_results_status ON archive.analysis_results(status);
CREATE INDEX idx_analysis_results_created_at ON archive.analysis_results(created_at);
```

## Dependencies

```json
{
  "express": "^4.18.2",
  "pg": "^8.10.0",
  "sequelize": "^6.31.0",
  "express-jwt": "^8.4.1",
  "joi": "^17.9.1",
  "cors": "^2.8.5",
  "dotenv": "^16.0.3"
}
```

## Environment Variables

```env
PORT=3002
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=atma_archive
DB_USER=postgres
DB_PASSWORD=password

# JWT Configuration (untuk verifikasi token)
JWT_SECRET=your_jwt_secret_key

# Auth Service URL (untuk verifikasi user)
AUTH_SERVICE_URL=http://localhost:3001

# Pagination Configuration
DEFAULT_PAGE_SIZE=10
MAX_PAGE_SIZE=100
```

## API Endpoints

### GET /archive/results
Mendapatkan daftar hasil analisis milik user

**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10, max: 100)
- `status` (optional): Filter by status ('completed', 'processing', 'failed')
- `sort` (optional): Sort by field ('created_at', 'updated_at') (default: 'created_at')
- `order` (optional): Sort order ('asc', 'desc') (default: 'desc')

**Response Success (200):**
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "user_id": "550e8400-e29b-41d4-a716-446655440001",
        "persona_profile": [
          {
            "archetype": "The Innovator",
            "shortSummary": "A creative problem-solver with strong analytical skills...",
            "strengths": ["Creative thinking", "Problem solving", "Analytical skills", "Adaptability"],
            "weakness": ["Impatience", "Perfectionism", "Overthinking", "Difficulty with routine"],
            "careerRecommendation": [
              {
                "career": "Software Engineer",
                "reason": "Combines analytical thinking with creative problem-solving"
              }
            ],
            "insights": ["Focus on developing patience", "Embrace structured approaches"],
            "workEnvironment": "Dynamic, collaborative environment with autonomy",
            "roleModel": ["Steve Jobs", "Elon Musk"]
          }
        ],
        "status": "completed",
        "created_at": "2024-01-01T00:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### GET /archive/results/:id
Mendapatkan detail hasil analisis berdasarkan ID

**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response Success (200):**
```json
{
  "success": true,
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "user_id": "550e8400-e29b-41d4-a716-446655440001",
    "assessment_data": {
      "riasec": {
        "realistic": 75,
        "investigative": 85,
        "artistic": 60,
        "social": 50,
        "enterprising": 70,
        "conventional": 55
      },
      "ocean": {
        "openness": 80,
        "conscientiousness": 65,
        "extraversion": 55,
        "agreeableness": 45,
        "neuroticism": 30
      },
      "viaIs": {
        "creativity": 85,
        "curiosity": 78,
        "judgment": 70,
        "loveOfLearning": 82,
        "perspective": 60,
        "bravery": 55,
        "perseverance": 68,
        "honesty": 73,
        "zest": 66,
        "love": 80,
        "kindness": 75,
        "socialIntelligence": 65,
        "teamwork": 60,
        "fairness": 70,
        "leadership": 67,
        "forgiveness": 58,
        "humility": 62,
        "prudence": 69,
        "selfRegulation": 61,
        "appreciationOfBeauty": 50,
        "gratitude": 72,
        "hope": 77,
        "humor": 65,
        "spirituality": 55
      },
      "multipleIntelligences": {
        "linguistic": 85,
        "logicalMathematical": 90,
        "spatial": 75,
        "bodilyKinesthetic": 60,
        "musical": 55,
        "interpersonal": 70,
        "intrapersonal": 65,
        "naturalistic": 50
      },
      "cognitiveStyleIndex": {
        "analytic": 80,
        "intuitive": 60
      }
    },
    "persona_profile": [
      {
        "archetype": "The Innovator",
        "shortSummary": "A creative problem-solver with strong analytical skills...",
        "strengths": ["Creative thinking", "Problem solving", "Analytical skills", "Adaptability"],
        "weakness": ["Impatience", "Perfectionism", "Overthinking", "Difficulty with routine"],
        "careerRecommendation": [
          {
            "career": "Software Engineer",
            "reason": "Combines analytical thinking with creative problem-solving"
          },
          {
            "career": "Data Scientist",
            "reason": "Leverages analytical skills and pattern recognition"
          }
        ],
        "insights": [
          "Focus on developing patience for long-term projects",
          "Embrace structured approaches to balance creativity",
          "Seek environments that value innovation"
        ],
        "workEnvironment": "Dynamic, collaborative environment with autonomy and creative freedom",
        "roleModel": ["Steve Jobs", "Elon Musk", "Marie Curie"]
      }
    ],
    "status": "completed",
    "created_at": "2024-01-01T00:00:00.000Z",
    "updated_at": "2024-01-01T00:00:00.000Z"
  }
}
```

**Response Error (404):**
```json
{
  "success": false,
  "error": {
    "code": "RESULT_NOT_FOUND",
    "message": "Analysis result not found or access denied"
  }
}
```

### POST /archive/results
Menyimpan hasil analisis baru (hanya untuk internal service)

**Headers:**
```
X-Internal-Service: true
X-Service-Key: internal_service_secret_key
Content-Type: application/json
```

**Request Body:**
```json
{
  "user_id": "550e8400-e29b-41d4-a716-446655440001",
  "assessment_data": {
    "riasec": {
      "realistic": 75,
      "investigative": 85,
      "artistic": 60,
      "social": 50,
      "enterprising": 70,
      "conventional": 55
    },
    "ocean": {
      "openness": 80,
      "conscientiousness": 65,
      "extraversion": 55,
      "agreeableness": 45,
      "neuroticism": 30
    },
    "viaIs": {
      "creativity": 85,
      "curiosity": 78,
      "judgment": 70,
      "loveOfLearning": 82,
      "perspective": 60,
      "bravery": 55,
      "perseverance": 68,
      "honesty": 73,
      "zest": 66,
      "love": 80,
      "kindness": 75,
      "socialIntelligence": 65,
      "teamwork": 60,
      "fairness": 70,
      "leadership": 67,
      "forgiveness": 58,
      "humility": 62,
      "prudence": 69,
      "selfRegulation": 61,
      "appreciationOfBeauty": 50,
      "gratitude": 72,
      "hope": 77,
      "humor": 65,
      "spirituality": 55
    },
    "multipleIntelligences": {
      "linguistic": 85,
      "logicalMathematical": 90,
      "spatial": 75,
      "bodilyKinesthetic": 60,
      "musical": 55,
      "interpersonal": 70,
      "intrapersonal": 65,
      "naturalistic": 50
    },
    "cognitiveStyleIndex": {
      "analytic": 80,
      "intuitive": 60
    }
  },
  "persona_profile": [
    {
      "archetype": "The Innovator",
      "shortSummary": "...",
      "strengths": [...],
      "weakness": [...],
      "careerRecommendation": [...],
      "insights": [...],
      "workEnvironment": "...",
      "roleModel": [...]
    }
  ],
  "status": "completed"
}
```

**Response Success (201):**
```json
{
  "success": true,
  "message": "Analysis result saved successfully",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "user_id": "550e8400-e29b-41d4-a716-446655440001",
    "status": "completed",
    "created_at": "2024-01-01T00:00:00.000Z"
  }
}
```

### PUT /archive/results/:id
Update hasil analisis (hanya untuk internal service)

**Headers:**
```
X-Internal-Service: true
X-Service-Key: internal_service_secret_key
Content-Type: application/json
```

**Request Body:**
```json
{
  "persona_profile": [...],
  "status": "completed"
}
```

**Response Success (200):**
```json
{
  "success": true,
  "message": "Analysis result updated successfully",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "updated_at": "2024-01-01T01:00:00.000Z"
  }
}
```

### DELETE /archive/results/:id
Hapus hasil analisis

**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response Success (200):**
```json
{
  "success": true,
  "message": "Analysis result deleted successfully"
}
```

### GET /archive/stats
Mendapatkan statistik hasil analisis user

**Headers:**
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**Response Success (200):**
```json
{
  "success": true,
  "data": {
    "total_analyses": 15,
    "completed": 12,
    "processing": 2,
    "failed": 1,
    "latest_analysis": "2024-01-01T00:00:00.000Z",
    "most_common_archetype": "The Innovator"
  }
}
```

## Health Check

### GET /health
**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "database": "connected",
  "version": "1.0.0"
}
```

## Error Codes

- `VALIDATION_ERROR`: Input validation failed
- `RESULT_NOT_FOUND`: Analysis result tidak ditemukan
- `ACCESS_DENIED`: User tidak memiliki akses ke resource
- `UNAUTHORIZED`: Token tidak valid atau expired
- `INTERNAL_ERROR`: Server error
- `DATABASE_ERROR`: Database connection error

## Security Features

1. **JWT Authentication**: Verifikasi token untuk setiap request
2. **User Isolation**: User hanya dapat mengakses data miliknya sendiri
3. **Internal Service Protection**: Endpoint internal dilindungi dengan service key
4. **Input Validation**: Menggunakan Joi untuk validasi input
5. **SQL Injection Protection**: Menggunakan Sequelize ORM
