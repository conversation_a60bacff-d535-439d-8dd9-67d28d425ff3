# ATMA Backend - Load Testing Guide untuk 100 Concurrent Users

## 📋 Executive Summary

Dokumen ini menyediakan panduan lengkap untuk mensimulasikan dan menangani 100 user yang login dan menggunakan layanan assessment/submit bersamaan dalam 1 jaringan yang sama.

### Key Findings
- **Current System Capacity**: ~10-15 concurrent users dengan konfigurasi default
- **Required Optimizations**: Database pool, RabbitMQ, Worker scaling, Network config
- **Recommended Worker Instances**: 6 instances dengan 5 concurrency each (30 total jobs)
- **Estimated Processing Time**: 3-4 menit untuk 100 assessments
- **Infrastructure Requirements**: 16GB RAM, 8 CPU cores, 1Gbps network

## 🚀 Quick Start

### 1. Jalankan Load Test
```bash
# Install dependencies
npm install axios uuid

# Run load test
run-load-test.bat

# Atau manual
node load-test-100-users.js
```

### 2. Start Multiple Workers
```bash
# Start 6 worker instances
start-multiple-workers.bat

# Atau manual per instance
cd analysis-worker
set WORKER_INSTANCE_ID=worker-1 && npm start
```

### 3. Monitor Results
```bash
# Check queue status
rabbitmqctl list_queues name messages consumers

# Monitor worker logs
tail -f analysis-worker/logs/analysis-worker-*.log

# Check system resources
htop
```

## 📊 Load Testing Results Analysis

### Expected Performance Metrics

#### Successful Scenario (Optimized)
```
✅ Login Success Rate: 95-100%
✅ Assessment Submit Success Rate: 95-100%
✅ Average Response Time: <3000ms
✅ Queue Processing: 3-4 minutes for 100 jobs
✅ System Stability: No crashes or timeouts
```

#### Warning Indicators
```
⚠️ Login Success Rate: 80-95%
⚠️ Assessment Submit Success Rate: 80-95%
⚠️ Average Response Time: 3000-8000ms
⚠️ Queue Processing: 5-8 minutes for 100 jobs
⚠️ High Memory Usage: >80% RAM utilization
```

#### Critical Issues
```
❌ Login Success Rate: <80%
❌ Assessment Submit Success Rate: <80%
❌ Average Response Time: >8000ms
❌ Queue Processing: >10 minutes for 100 jobs
❌ System Crashes: OOM errors, connection timeouts
```

## ⚙️ Configuration Optimizations

### 1. Database Configuration
```javascript
// Current (Default)
pool: { max: 5, min: 0, acquire: 30000, idle: 10000 }

// Optimized untuk 100 users
pool: { max: 25, min: 5, acquire: 60000, idle: 30000 }
```

### 2. RabbitMQ Configuration
```bash
# Current
WORKER_CONCURRENCY=3

# Optimized
WORKER_CONCURRENCY=5
PREFETCH_COUNT=5
vm_memory_high_watermark.relative=0.6
```

### 3. Analysis Worker Scaling
```bash
# Current: 1 worker instance
# Optimized: 6 worker instances

Total Capacity: 6 workers × 5 concurrency = 30 concurrent jobs
Processing Rate: ~25-30 jobs per minute
Queue Completion: 3-4 minutes untuk 100 jobs
```

## 🏗️ Infrastructure Requirements

### Server Specifications
```yaml
ATMA Backend Server:
  CPU: 8 cores (Intel i7/AMD Ryzen 7)
  RAM: 16GB minimum, 32GB recommended
  Storage: 500GB SSD
  Network: 1Gbps Ethernet

Database Server:
  CPU: 4 cores minimum
  RAM: 8GB minimum, 16GB recommended  
  Storage: 1TB SSD dengan RAID 1
  Network: 1Gbps Ethernet

Message Queue Server:
  CPU: 4 cores minimum
  RAM: 8GB minimum
  Storage: 200GB SSD
  Network: 1Gbps Ethernet
```

### Network Configuration
```
Network: ***********/24
Server IPs: ***********-10
Client IPs: *************-199
Bandwidth: 10Mbps minimum internet, 1Gbps internal
Load Balancer: Nginx dengan rate limiting
```

## 📈 Scaling Recommendations

### Immediate Actions (Required)
1. **Increase Database Connections**: 5 → 25 per service
2. **Scale Analysis Workers**: 1 → 6 instances
3. **Optimize RabbitMQ**: Increase memory limits dan connection pools
4. **Add Load Balancer**: Nginx untuk rate limiting dan distribution

### Performance Improvements (Recommended)
1. **Implement Connection Pooling**: Keep-alive connections
2. **Add Caching Layer**: Redis untuk session dan temporary data
3. **Optimize AI Processing**: Batch requests, faster models
4. **Database Indexing**: Optimize queries untuk user operations

### High Availability (Optional)
1. **Database Clustering**: PostgreSQL master-slave setup
2. **RabbitMQ Clustering**: Multi-node message queue
3. **Load Balancer Redundancy**: Multiple Nginx instances
4. **Auto-scaling**: Docker/Kubernetes deployment

## 🔧 Implementation Steps

### Step 1: Database Optimization
```bash
# Update database configuration
# Edit: auth-service/src/config/database.js
# Edit: archive-service/src/config/database.js

# Apply new pool settings
pool: {
  max: 25,
  min: 5,
  acquire: 60000,
  idle: 30000,
  evict: 10000,
  handleDisconnects: true
}
```

### Step 2: Worker Scaling
```bash
# Start multiple workers
start-multiple-workers.bat

# Verify workers are running
tasklist | findstr node.exe

# Monitor worker logs
tail -f analysis-worker/logs/analysis-worker-*.log
```

### Step 3: RabbitMQ Optimization
```bash
# Apply RabbitMQ configuration
copy rabbitmq-optimization.conf /etc/rabbitmq/rabbitmq.conf

# Setup policies
bash setup-rabbitmq-policies.sh

# Restart RabbitMQ
systemctl restart rabbitmq-server
```

### Step 4: Load Testing
```bash
# Run load test
CONCURRENT_USERS=100 node load-test-100-users.js

# Monitor system during test
htop
iftop -i eth0
rabbitmqctl list_queues
```

## 📊 Monitoring dan Alerting

### Key Metrics to Monitor
```bash
# System Metrics
CPU Usage: <80%
Memory Usage: <80%
Disk I/O: <80%
Network Bandwidth: <80%

# Application Metrics
Response Time: <3000ms
Error Rate: <5%
Queue Length: <100 messages
Worker Active Jobs: <30

# Database Metrics
Active Connections: <20 per service
Query Time: <1000ms
Lock Waits: <10 per minute
```

### Monitoring Commands
```bash
# System monitoring
htop
iostat -x 1
vmstat 1
iftop -i eth0

# Application monitoring
curl http://localhost:3000/health
rabbitmqctl list_queues name messages consumers
tail -f */logs/*.log

# Database monitoring
psql -c "SELECT count(*) FROM pg_stat_activity;"
psql -c "SELECT query, state, query_start FROM pg_stat_activity WHERE state = 'active';"
```

## 🚨 Troubleshooting Guide

### Common Issues dan Solutions

#### 1. High Login Failure Rate
**Symptoms**: >20% login failures
**Causes**: Database connection exhaustion, auth service overload
**Solutions**:
- Increase database connection pool
- Add connection retry logic
- Implement rate limiting

#### 2. Assessment Submit Timeouts
**Symptoms**: 504 Gateway Timeout errors
**Causes**: Queue service overload, worker capacity exceeded
**Solutions**:
- Scale analysis workers
- Increase processing timeout
- Optimize queue configuration

#### 3. Queue Backup
**Symptoms**: Queue message count continuously increasing
**Causes**: Insufficient worker capacity, AI API rate limits
**Solutions**:
- Add more worker instances
- Increase worker concurrency
- Implement queue monitoring

#### 4. Memory Issues
**Symptoms**: OOM errors, system crashes
**Causes**: Memory leaks, insufficient RAM, large job payloads
**Solutions**:
- Increase system RAM
- Optimize job tracker memory usage
- Add memory monitoring

#### 5. Network Congestion
**Symptoms**: High latency, packet loss
**Causes**: Bandwidth saturation, switch overload
**Solutions**:
- Upgrade network infrastructure
- Implement QoS policies
- Add network monitoring

## 📋 Testing Checklist

### Pre-Test Checklist
- [ ] All services running dan healthy
- [ ] Database connections optimized
- [ ] Multiple workers started
- [ ] RabbitMQ configured dan running
- [ ] Network infrastructure ready
- [ ] Monitoring tools active

### During Test Checklist
- [ ] Monitor system resources (CPU, RAM, Network)
- [ ] Track application metrics (response times, errors)
- [ ] Watch queue status dan worker logs
- [ ] Record performance data
- [ ] Note any errors atau issues

### Post-Test Checklist
- [ ] Analyze test results
- [ ] Review error logs
- [ ] Calculate performance metrics
- [ ] Document findings
- [ ] Plan optimization actions
- [ ] Clean up test data

## 📝 Conclusion

Dengan implementasi optimasi yang tepat, sistem ATMA backend dapat menangani 100 concurrent users dengan performa yang baik. Key success factors:

1. **Database Scaling**: Connection pool optimization
2. **Worker Scaling**: 6 instances dengan proper concurrency
3. **Infrastructure**: Adequate server resources dan network
4. **Monitoring**: Comprehensive monitoring dan alerting
5. **Testing**: Regular load testing untuk validation

Estimated total implementation time: 2-3 hari untuk full optimization.
